import React, { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  MapPin,
  Navigation,
  Clock,
  Car,
  Plane,
  ShoppingBag,
  Utensils,
  GraduationCap,
  Building,
  Trees,
  Waves,
  Mountain,
} from "lucide-react";

const Location = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [activeTab, setActiveTab] = useState("amenities");

  const nearbyAmenities = [
    {
      icon: ShoppingBag,
      name: "Rodeo Drive",
      distance: "0.5 miles",
      type: "Shopping",
      description: "World-class luxury shopping destination",
      color: "bg-pink-500/20 text-pink-400",
    },
    {
      icon: Utensils,
      name: "Michelin Star Restaurants",
      distance: "0.3 miles",
      type: "Dining",
      description: "Fine dining establishments",
      color: "bg-orange-500/20 text-orange-400",
    },
    {
      icon: GraduationCap,
      name: "Beverly Hills High School",
      distance: "1.2 miles",
      type: "Education",
      description: "Top-rated educational institution",
      color: "bg-blue-500/20 text-blue-400",
    },
    {
      icon: Building,
      name: "Century City",
      distance: "2.1 miles",
      type: "Business",
      description: "Major business district",
      color: "bg-gray-500/20 text-gray-400",
    },
    {
      icon: Trees,
      name: "Beverly Gardens Park",
      distance: "0.8 miles",
      type: "Recreation",
      description: "Beautiful landscaped park",
      color: "bg-green-500/20 text-green-400",
    },
    {
      icon: Waves,
      name: "Santa Monica Beach",
      distance: "8.5 miles",
      type: "Beach",
      description: "Pristine oceanfront recreation",
      color: "bg-cyan-500/20 text-cyan-400",
    },
  ];

  const transportation = [
    {
      icon: Car,
      name: "Downtown LA",
      time: "25 minutes",
      method: "Drive",
    },
    {
      icon: Plane,
      name: "LAX Airport",
      time: "35 minutes",
      method: "Drive",
    },
    {
      icon: Car,
      name: "Hollywood",
      time: "20 minutes",
      method: "Drive",
    },
    {
      icon: Car,
      name: "Malibu",
      time: "45 minutes",
      method: "Drive",
    },
  ];

  const neighborhoods = [
    {
      name: "Beverly Hills",
      image:
        "https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=800",
      description: "Exclusive residential area with luxury shopping and dining",
      highlights: ["Rodeo Drive", "Beverly Hills Hotel", "Greystone Mansion"],
    },
    {
      name: "West Hollywood",
      image:
        "https://images.pexels.com/photos/1643383/pexels-photo-1643383.jpeg?auto=compress&cs=tinysrgb&w=800",
      description: "Vibrant nightlife and entertainment district",
      highlights: ["Sunset Strip", "Design District", "Melrose Avenue"],
    },
    {
      name: "Bel Air",
      image:
        "https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800",
      description: "Ultra-exclusive gated community with sprawling estates",
      highlights: ["Bel Air Country Club", "UCLA", "Getty Center"],
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="location" className="py-20 bg-accent">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-16"
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center space-y-6">
            <h2 className="text-section-mobile md:text-section font-playfair font-bold text-primary">
              Prime Location
            </h2>
            <div className="w-20 h-1 bg-secondary mx-auto" />
            <p className="text-body-mobile md:text-body text-charcoal font-montserrat max-w-2xl mx-auto">
              Situated in the heart of Beverly Hills, our properties offer
              unparalleled access to the finest amenities, dining, and cultural
              attractions Los Angeles has to offer.
            </p>
          </motion.div>

          {/* Interactive Map Section */}
          <motion.div variants={itemVariants} className="space-y-8">
            <div className="bg-white rounded-2xl shadow-luxury overflow-hidden">
              {/* Map Placeholder */}
              <div className="relative h-96 bg-gradient-to-br from-primary to-primary/80">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center space-y-4">
                    <MapPin className="h-16 w-16 text-secondary mx-auto" />
                    <h3 className="text-2xl font-playfair font-bold text-accent">
                      Interactive Map
                    </h3>
                    <p className="text-accent/80 font-montserrat">
                      Explore the neighborhood and nearby amenities
                    </p>
                    <motion.button
                      className="bg-secondary hover:bg-secondary/90 text-primary px-6 py-3 rounded-lg font-montserrat font-semibold transition-all duration-300"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Navigation className="h-5 w-5 inline mr-2" />
                      Open Full Map
                    </motion.button>
                  </div>
                </div>

                {/* Map Markers */}
                <div className="absolute top-1/4 left-1/3 animate-bounce">
                  <div className="w-4 h-4 bg-secondary rounded-full border-2 border-white shadow-lg" />
                </div>
                <div className="absolute top-1/2 right-1/3 animate-bounce delay-300">
                  <div className="w-4 h-4 bg-error rounded-full border-2 border-white shadow-lg" />
                </div>
                <div className="absolute bottom-1/3 left-1/2 animate-bounce delay-700">
                  <div className="w-4 h-4 bg-success rounded-full border-2 border-white shadow-lg" />
                </div>
              </div>
            </div>
          </motion.div>

          {/* Tabs Navigation */}
          <motion.div variants={itemVariants} className="flex justify-center">
            <div className="flex space-x-1 bg-white rounded-full p-2 shadow-lg">
              {[
                { id: "amenities", label: "Nearby Amenities" },
                { id: "transport", label: "Transportation" },
                { id: "neighborhoods", label: "Neighborhoods" },
              ].map((tab) => (
                <motion.button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-6 py-3 rounded-full font-montserrat font-medium transition-all duration-300 ${
                    activeTab === tab.id
                      ? "bg-primary text-accent shadow-lg"
                      : "text-charcoal hover:text-primary"
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {tab.label}
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Tab Content */}
          <motion.div variants={itemVariants}>
            {activeTab === "amenities" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              >
                {nearbyAmenities.map((amenity, index) => (
                  <motion.div
                    key={index}
                    className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group"
                    whileHover={{ y: -5 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="flex items-start space-x-4">
                      <div
                        className={`w-12 h-12 rounded-full ${amenity.color} flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}
                      >
                        <amenity.icon className="h-6 w-6" />
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-playfair font-semibold text-primary">
                            {amenity.name}
                          </h4>
                          <span className="text-secondary font-montserrat text-sm font-medium">
                            {amenity.distance}
                          </span>
                        </div>
                        <p className="text-xs text-secondary font-montserrat font-medium mb-1">
                          {amenity.type}
                        </p>
                        <p className="text-charcoal/70 font-montserrat text-sm">
                          {amenity.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}

            {activeTab === "transport" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-8"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {transportation.map((transport, index) => (
                    <motion.div
                      key={index}
                      className="bg-white rounded-xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300"
                      whileHover={{ y: -5, scale: 1.02 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <transport.icon className="h-8 w-8 text-primary" />
                      </div>
                      <h4 className="font-playfair font-semibold text-primary mb-2">
                        {transport.name}
                      </h4>
                      <div className="flex items-center justify-center space-x-2 text-secondary">
                        <Clock className="h-4 w-4" />
                        <span className="font-montserrat text-sm">
                          {transport.time}
                        </span>
                      </div>
                      <p className="text-charcoal/70 font-montserrat text-xs mt-1">
                        by {transport.method}
                      </p>
                    </motion.div>
                  ))}
                </div>

                <div className="bg-primary rounded-2xl p-8 text-center">
                  <h3 className="text-2xl font-playfair font-bold text-accent mb-4">
                    Convenient Transportation Hub
                  </h3>
                  <p className="text-accent/90 font-montserrat max-w-2xl mx-auto">
                    Located at the crossroads of Los Angeles' most desirable
                    destinations, our properties offer unmatched accessibility
                    to business districts, entertainment venues, and cultural
                    landmarks.
                  </p>
                </div>
              </motion.div>
            )}

            {activeTab === "neighborhoods" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="grid grid-cols-1 lg:grid-cols-3 gap-8"
              >
                {neighborhoods.map((neighborhood, index) => (
                  <motion.div
                    key={index}
                    className="bg-white rounded-2xl overflow-hidden shadow-luxury group"
                    whileHover={{ y: -8 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="relative h-48 overflow-hidden">
                      <img
                        src={neighborhood.image}
                        alt={neighborhood.name}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                        loading="lazy"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-primary/50 to-transparent" />
                      <div className="absolute bottom-4 left-4">
                        <h3 className="text-xl font-playfair font-bold text-accent">
                          {neighborhood.name}
                        </h3>
                      </div>
                    </div>

                    <div className="p-6 space-y-4">
                      <p className="text-charcoal font-montserrat leading-relaxed">
                        {neighborhood.description}
                      </p>

                      <div className="space-y-2">
                        <h4 className="font-montserrat font-semibold text-primary text-sm">
                          Key Highlights:
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {neighborhood.highlights.map((highlight, idx) => (
                            <span
                              key={idx}
                              className="px-3 py-1 bg-accent rounded-full text-xs font-montserrat text-charcoal"
                            >
                              {highlight}
                            </span>
                          ))}
                        </div>
                      </div>

                      <motion.button
                        className="w-full bg-primary hover:bg-primary/90 text-accent py-3 rounded-lg font-montserrat font-medium transition-all duration-300"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        Explore Area
                      </motion.button>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Location;
