import React, { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { MapPin, Home, Navigation } from "lucide-react";
import { properties } from "../data/properties";

const PropertyMap = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [selectedProperty, setSelectedProperty] = useState(null);

  // Indian cities with their approximate positions on a map
  const cityPositions = {
    "Bandra West, Mumbai": { x: 25, y: 70 },
    "Golf Course Road, Gurgaon": { x: 45, y: 35 },
    "Koramangala, Bangalore": { x: 40, y: 85 },
    "Vasant Vihar, Delhi": { x: 45, y: 30 },
    "Koregaon Park, Pune": { x: 35, y: 65 },
    "Jubilee Hills, Hyderabad": { x: 50, y: 75 },
  };

  const formatPrice = (price) => {
    if (price >= 10000000) {
      return `₹${(price / 10000000).toFixed(1)} Cr`;
    }
    return `₹${(price / 100000).toFixed(1)} L`;
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="map" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-12"
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center space-y-6">
            <h2 className="text-section-mobile md:text-section font-heading font-bold text-primary">
              Property Locations
            </h2>
            <div className="w-20 h-1 bg-secondary mx-auto" />
            <p className="text-body-lg md:text-body-lg text-charcoal font-body max-w-2xl mx-auto">
              Explore our premium properties across India's most prestigious
              locations
            </p>
          </motion.div>

          {/* Map Container */}
          <motion.div variants={itemVariants} className="relative">
            <div className="bg-gradient-to-br from-blue-50 to-green-50 rounded-2xl p-8 shadow-luxury">
              {/* India Map Outline */}
              <div className="relative w-full h-96 mx-auto max-w-2xl">
                {/* India Map SVG Background */}
                <svg
                  viewBox="0 0 400 400"
                  className="absolute inset-0 w-full h-full"
                  style={{ filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.1))" }}
                >
                  {/* Simplified India outline */}
                  <path
                    d="M120 80 L180 60 L220 70 L260 80 L280 100 L290 130 L300 160 L310 200 L300 240 L290 280 L270 320 L240 340 L200 350 L160 340 L130 320 L110 280 L100 240 L90 200 L100 160 L110 120 Z"
                    fill="url(#indiaGradient)"
                    stroke="#2D5016"
                    strokeWidth="2"
                    className="opacity-80"
                  />
                  <defs>
                    <linearGradient
                      id="indiaGradient"
                      x1="0%"
                      y1="0%"
                      x2="100%"
                      y2="100%"
                    >
                      <stop offset="0%" stopColor="#10B981" stopOpacity="0.3" />
                      <stop
                        offset="50%"
                        stopColor="#059669"
                        stopOpacity="0.2"
                      />
                      <stop
                        offset="100%"
                        stopColor="#047857"
                        stopOpacity="0.1"
                      />
                    </linearGradient>
                  </defs>
                </svg>

                {/* Map Title */}
                <div className="absolute top-4 left-4 flex items-center space-x-2 bg-white/90 rounded-lg px-3 py-1 shadow-md">
                  <Navigation className="h-4 w-4 text-primary" />
                  <span className="font-accent font-semibold text-primary text-body-sm">
                    Property Locations in India
                  </span>
                </div>

                {/* Property Markers */}
                {properties.map((property, index) => {
                  const position = cityPositions[property.location];
                  if (!position) return null;

                  return (
                    <motion.div
                      key={property.id}
                      className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
                      style={{
                        left: `${position.x}%`,
                        top: `${position.y}%`,
                      }}
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ delay: index * 0.2, duration: 0.5 }}
                      whileHover={{ scale: 1.2 }}
                      onClick={() => setSelectedProperty(property)}
                    >
                      <div className="relative group">
                        <div className="w-5 h-5 bg-primary rounded-full border-3 border-white shadow-lg transition-all duration-300 group-hover:scale-125">
                          <div className="absolute inset-0 bg-secondary rounded-full animate-pulse opacity-50"></div>
                        </div>
                        <div className="absolute -top-1 -left-1 w-7 h-7 bg-primary/20 rounded-full animate-ping"></div>

                        {/* Property count badge */}
                        <div className="absolute -top-2 -right-2 w-4 h-4 bg-secondary text-primary text-xs rounded-full flex items-center justify-center font-bold shadow-md">
                          {
                            properties.filter(
                              (p) => p.location === property.location
                            ).length
                          }
                        </div>
                      </div>
                    </motion.div>
                  );
                })}

                {/* Property Info Popup */}
                {selectedProperty && (
                  <motion.div
                    className="absolute z-10 bg-white rounded-lg shadow-xl p-4 max-w-xs"
                    style={{
                      left: `${cityPositions[selectedProperty.location]?.x}%`,
                      top: `${
                        cityPositions[selectedProperty.location]?.y - 15
                      }%`,
                    }}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="space-y-2">
                      <h3 className="font-playfair font-semibold text-primary text-sm">
                        {selectedProperty.title}
                      </h3>
                      <div className="flex items-center space-x-1 text-xs text-charcoal/70">
                        <MapPin className="h-3 w-3" />
                        <span>{selectedProperty.location}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="font-montserrat font-bold text-primary">
                          {formatPrice(selectedProperty.price)}
                        </span>
                        <div className="flex items-center space-x-1 text-xs text-charcoal/70">
                          <Home className="h-3 w-3" />
                          <span>{selectedProperty.bedrooms}BHK</span>
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => setSelectedProperty(null)}
                      className="absolute -top-2 -right-2 w-6 h-6 bg-charcoal/10 hover:bg-charcoal/20 rounded-full flex items-center justify-center text-xs"
                    >
                      ×
                    </button>
                  </motion.div>
                )}

                {/* Map Legend */}
                <div className="absolute bottom-4 right-4 bg-white/95 rounded-lg p-3 shadow-md">
                  <h4 className="font-montserrat font-semibold text-xs text-primary mb-2">
                    Legend
                  </h4>
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-primary rounded-full border border-white"></div>
                      <span className="text-xs text-charcoal/70">
                        Property Location
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-secondary rounded-full text-primary text-xs flex items-center justify-center font-bold">
                        1
                      </div>
                      <span className="text-xs text-charcoal/70">
                        Property Count
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Location Stats */}
          <motion.div variants={itemVariants}>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
              {Object.keys(cityPositions).map((city, index) => {
                const cityProperties = properties.filter(
                  (p) => p.location === city
                );
                const avgPrice =
                  cityProperties.reduce((sum, p) => sum + p.price, 0) /
                  cityProperties.length;

                return (
                  <motion.div
                    key={city}
                    className="text-center p-4 bg-accent rounded-lg hover:shadow-md transition-shadow duration-300"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.5 }}
                  >
                    <div className="w-3 h-3 bg-primary rounded-full mx-auto mb-2"></div>
                    <h4 className="font-montserrat font-semibold text-xs text-primary mb-1">
                      {city.split(",")[0]}
                    </h4>
                    <p className="text-xs text-charcoal/70 mb-1">
                      {cityProperties.length}{" "}
                      {cityProperties.length === 1 ? "Property" : "Properties"}
                    </p>
                    <p className="text-xs font-semibold text-primary">
                      Avg: {formatPrice(avgPrice)}
                    </p>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default PropertyMap;
