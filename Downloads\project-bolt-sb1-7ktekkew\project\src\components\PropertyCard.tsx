import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  Bed,
  Bath,
  Ruler,
  MapPin,
  Heart,
  Share2,
  Eye,
  Car,
  Calendar,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { Property } from "../types";

interface PropertyCardProps {
  property: Property;
  featured?: boolean;
}

const PropertyCard: React.FC<PropertyCardProps> = ({
  property,
  featured = false,
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLiked, setIsLiked] = useState(false);

  const formatPrice = (price: number) => {
    if (price >= 10000000) {
      return `₹${(price / 10000000).toFixed(1)} Cr`;
    }
    return `₹${(price / 100000).toFixed(1)} L`;
  };

  const nextImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentImageIndex((prev) => (prev + 1) % property.images.length);
  };

  const prevImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentImageIndex(
      (prev) => (prev - 1 + property.images.length) % property.images.length
    );
  };

  const handleLike = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsLiked(!isLiked);
  };

  const handleShare = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (navigator.share) {
      navigator.share({
        title: property.title,
        text: property.description,
        url: window.location.href,
      });
    }
  };

  return (
    <motion.div
      className="bg-white rounded-2xl shadow-luxury overflow-hidden group cursor-pointer"
      whileHover={{ y: -8, scale: 1.02 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      layout
    >
      {/* Image Gallery */}
      <div className="relative overflow-hidden">
        <div
          className="relative h-64"
          style={{
            backgroundImage: `url(${property.images[currentImageIndex]})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        >
          {/* Status Badge */}
          <div className="absolute top-4 left-4 z-10">
            <span
              className={`px-3 py-1 rounded-full text-xs font-montserrat font-semibold ${
                property.status === "for-sale"
                  ? "bg-success text-white"
                  : property.status === "for-rent"
                  ? "bg-warning text-white"
                  : "bg-charcoal text-white"
              }`}
            >
              {property.status === "for-sale"
                ? "For Sale"
                : property.status === "for-rent"
                ? "For Rent"
                : property.status === "sold"
                ? "Sold"
                : "Rented"}
            </span>
          </div>

          {/* Featured Badge */}
          {property.featured && (
            <div className="absolute top-4 right-4 z-10">
              <span className="bg-secondary text-primary px-3 py-1 rounded-full text-xs font-montserrat font-semibold">
                Featured
              </span>
            </div>
          )}

          {/* Image Navigation */}
          {property.images.length > 1 && (
            <>
              <button
                onClick={prevImage}
                className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 z-10"
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
              <button
                onClick={nextImage}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 z-10"
              >
                <ChevronRight className="h-4 w-4" />
              </button>

              {/* Image Indicators */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
                {property.images.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      index === currentImageIndex ? "bg-white" : "bg-white/50"
                    }`}
                  />
                ))}
              </div>
            </>
          )}

          {/* Action Buttons */}
          <div className="absolute top-4 right-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-all duration-300 z-10">
            <motion.button
              onClick={handleLike}
              className={`p-2 rounded-full backdrop-blur-sm transition-all duration-300 ${
                isLiked
                  ? "bg-error text-white"
                  : "bg-white/20 text-white hover:bg-white/30"
              }`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Heart className={`h-4 w-4 ${isLiked ? "fill-current" : ""}`} />
            </motion.button>

            <motion.button
              onClick={handleShare}
              className="p-2 rounded-full bg-white/20 text-white hover:bg-white/30 backdrop-blur-sm transition-all duration-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Share2 className="h-4 w-4" />
            </motion.button>
          </div>

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />
        </div>
      </div>

      {/* Content */}
      <div className="p-6 space-y-4">
        {/* Price and Location */}
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-price text-heading-md text-primary">
              {formatPrice(property.price)}
            </h3>
            <div className="flex items-center space-x-1 text-charcoal/70 mt-1">
              <MapPin className="h-4 w-4" />
              <span className="text-location text-body-sm">
                {property.location}
              </span>
            </div>
          </div>

          <div className="text-right">
            <span className="text-caption font-accent text-charcoal/70 uppercase tracking-wider font-medium">
              {property.type}
            </span>
          </div>
        </div>

        {/* Title */}
        <h4 className="font-heading font-semibold text-charcoal text-heading-sm">
          {property.title}
        </h4>

        {/* Property Details */}
        <div className="grid grid-cols-4 gap-4 py-3 border-t border-gray-200">
          <div className="text-center">
            <Bed className="h-5 w-5 text-secondary mx-auto mb-1" />
            <span className="text-body-sm font-accent font-semibold text-charcoal">
              {property.bedrooms}
            </span>
            <div className="text-caption text-charcoal/70 font-body">Beds</div>
          </div>

          <div className="text-center">
            <Bath className="h-5 w-5 text-secondary mx-auto mb-1" />
            <span className="text-sm font-montserrat text-charcoal">
              {property.bathrooms}
            </span>
            <div className="text-xs text-charcoal/70 font-montserrat">
              Baths
            </div>
          </div>

          <div className="text-center">
            <Ruler className="h-5 w-5 text-secondary mx-auto mb-1" />
            <span className="text-sm font-montserrat text-charcoal">
              {property.area.toLocaleString()}
            </span>
            <div className="text-xs text-charcoal/70 font-montserrat">
              Sq Ft
            </div>
          </div>

          <div className="text-center">
            <Car className="h-5 w-5 text-secondary mx-auto mb-1" />
            <span className="text-sm font-montserrat text-charcoal">
              {property.parking}
            </span>
            <div className="text-xs text-charcoal/70 font-montserrat">
              Parking
            </div>
          </div>
        </div>

        {/* Features (for featured properties) */}
        {featured && (
          <div className="space-y-2">
            <h5 className="font-montserrat font-semibold text-charcoal text-sm">
              Key Features:
            </h5>
            <div className="flex flex-wrap gap-2">
              {property.features.slice(0, 4).map((feature, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-accent rounded-full text-xs font-montserrat text-charcoal"
                >
                  {feature}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-3 pt-4">
          <motion.button
            className="flex-1 bg-primary hover:bg-primary/90 text-accent px-4 py-2 rounded-lg font-montserrat font-medium transition-all duration-300 flex items-center justify-center space-x-2"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Eye className="h-4 w-4" />
            <span>View Details</span>
          </motion.button>

          <motion.button
            className="px-4 py-2 border border-secondary text-secondary hover:bg-secondary hover:text-primary rounded-lg font-montserrat font-medium transition-all duration-300"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            Contact
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

export default PropertyCard;
