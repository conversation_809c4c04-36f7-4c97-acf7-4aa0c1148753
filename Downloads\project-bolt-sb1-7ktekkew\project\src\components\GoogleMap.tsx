import React from "react";
import { motion } from "framer-motion";
import { MapPin, ExternalLink, Navigation } from "lucide-react";

const GoogleMap = () => {
  // Devanahalli coordinates: 13.2846° N, 77.6641° E
  const devanahalli = {
    lat: 13.2846,
    lng: 77.6641,
    name: "<PERSON><PERSON><PERSON><PERSON> Infra Projects - Devanahalli",
    address: "Devanahalli, North Bengaluru, Karnataka 562110",
  };

  // Google Maps embed URL for Devanahalli area
  const mapEmbedUrl = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d15543.123456789!2d77.6641!3d13.2846!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3bae1670c9b44e6d%3A0x21312a92fd5c696e!2sDevanahalli%2C%20Karnataka!5e0!3m2!1sen!2sin!4v1699123456789!5m2!1sen!2sin`;

  // Google Maps directions URL
  const directionsUrl = `https://www.google.com/maps/dir/?api=1&destination=${devanahalli.lat},${devanahalli.lng}&destination_place_id=ChIJX8X8X8X8X8X8X8X8X8X8X8`;

  // Google Maps place URL
  const placeUrl = `https://www.google.com/maps/place/${devanahalli.lat},${devanahalli.lng}/@${devanahalli.lat},${devanahalli.lng},15z`;

  const nearbyLandmarks = [
    {
      name: "Kempegowda International Airport",
      distance: "20 min",
      type: "Airport",
    },
    { name: "Foxconn iPhone Campus", distance: "15 min", type: "Employment" },
    { name: "SAP Labs", distance: "18 min", type: "Employment" },
    { name: "Nandi Hills", distance: "15 min", type: "Tourist Spot" },
    {
      name: "Harrow International School",
      distance: "10 min",
      type: "Education",
    },
    { name: "NH-44 Highway", distance: "5 min", type: "Highway" },
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 p-10">
      <h2 className="text-3xl font-playfair font-bold text-primary mb-6 ">
        Project Location
      </h2>

      {/* Google Maps Embed */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="bg-white rounded-2xl shadow-luxury overflow-hidden"
      >
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <MapPin className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="text-xl font-playfair font-bold text-primary">
                  Project Location
                </h3>
                <p className="text-charcoal/70 font-montserrat text-sm">
                  {devanahalli.address}
                </p>
              </div>
            </div>
            <div className="flex space-x-2">
              <motion.a
                href={directionsUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 bg-primary text-accent px-4 py-2 rounded-lg font-montserrat font-medium hover:bg-primary/90 transition-all duration-300"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Navigation className="h-4 w-4" />
                <span>Directions</span>
              </motion.a>
              <motion.a
                href={placeUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 border border-primary text-primary px-4 py-2 rounded-lg font-montserrat font-medium hover:bg-primary/5 transition-all duration-300"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <ExternalLink className="h-4 w-4" />
                <span>View on Maps</span>
              </motion.a>
            </div>
          </div>
        </div>

        {/* Google Maps Iframe */}
        <div className="relative h-96">
          <iframe
            src={mapEmbedUrl}
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allowFullScreen
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
            title="Shreyas Infra Projects Devanahalli Location"
            className="w-full h-full"
          />
        </div>
      </motion.div>

      {/* Nearby Landmarks */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className="bg-white rounded-2xl p-6 shadow-luxury mt-10 "
      >
        <h4 className="text-lg font-playfair font-bold text-primary mb-6">
          Nearby Landmarks & Connectivity
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {nearbyLandmarks.map((landmark, index) => (
            <motion.div
              key={index}
              className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-all duration-300"
              whileHover={{ scale: 1.02 }}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div
                className={`w-3 h-3 rounded-full ${
                  landmark.type === "Airport"
                    ? "bg-blue-500"
                    : landmark.type === "Employment"
                    ? "bg-purple-500"
                    : landmark.type === "Education"
                    ? "bg-green-500"
                    : landmark.type === "Highway"
                    ? "bg-orange-500"
                    : "bg-red-500"
                }`}
              />
              <div className="flex-1">
                <div className="font-montserrat font-medium text-charcoal text-sm">
                  {landmark.name}
                </div>
                <div className="text-xs text-charcoal/60">
                  {landmark.distance} • {landmark.type}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Location Benefits */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
        className="bg-gradient-to-r from-primary to-primary/80 rounded-2xl p-8 text-center mt-10"
      >
        <h4 className="text-2xl font-playfair font-bold text-accent mb-4">
          Strategic Location Advantages
        </h4>
        <p className="text-accent/90 font-montserrat max-w-2xl mx-auto mb-6">
          Located in North Bengaluru's fastest-growing corridor, our Devanahalli
          project offers unparalleled connectivity to major employment hubs,
          international airport, and premium educational institutions.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-3xl font-playfair font-bold text-secondary mb-2">
              20 min
            </div>
            <div className="text-accent/80 font-montserrat text-sm">
              To Airport
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-playfair font-bold text-secondary mb-2">
              ₹29,000+
            </div>
            <div className="text-accent/80 font-montserrat text-sm">
              Crores Investment Nearby
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-playfair font-bold text-secondary mb-2">
              1,20,000+
            </div>
            <div className="text-accent/80 font-montserrat text-sm">
              Jobs Being Created
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default GoogleMap;
