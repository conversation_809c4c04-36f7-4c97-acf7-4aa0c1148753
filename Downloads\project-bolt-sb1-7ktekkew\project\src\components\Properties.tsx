import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import PropertyCard from "./PropertyCard";
import { properties } from "../data/properties";

const Properties = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="properties" className="py-20 bg-accent">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-12"
        >
          <motion.div variants={itemVariants} className="text-center space-y-6">
            <h2 className="text-section-mobile md:text-section font-heading font-bold text-primary">
              Exclusive Properties
            </h2>
            <div className="w-20 h-1 bg-secondary mx-auto" />
            <p className="text-body-lg md:text-body-lg text-charcoal font-body max-w-2xl mx-auto">
              Discover our curated collection of luxury properties, each
              offering unparalleled elegance and exceptional value in prime
              locations.
            </p>
          </motion.div>

          <motion.div variants={itemVariants}>
            <div className="grid gap-8 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
              {properties.map((property, index) => (
                <motion.div
                  key={property.id}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <PropertyCard property={property} />
                </motion.div>
              ))}
            </div>
          </motion.div>

          {properties.length > 6 && (
            <motion.div variants={itemVariants} className="text-center">
              <motion.button
                className="bg-white hover:bg-gray-50 text-primary border-2 border-primary px-8 py-4 rounded-lg font-montserrat font-semibold transition-all duration-300"
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                Load More Properties
              </motion.button>
            </motion.div>
          )}
        </motion.div>
      </div>
    </section>
  );
};

export default Properties;
