import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { properties } from "../data/properties";

const Properties = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94] as const,
      },
    },
  };

  return (
    <section
      id="properties"
      className="min-h-screen flex items-center bg-accent py-6 lg:py-8 xl:py-12"
    >
      <div className="max-w-9xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-8 h-full"
        >
          <motion.div
            variants={itemVariants}
            className="text-center space-y-4 mb-6"
          >
            <h2 className="text-2xl md:text-4xl font-heading font-bold text-primary">
              Devanahalli Premium Plotted Development
            </h2>
            <div className="w-16 h-1 bg-secondary mx-auto" />
            <p className="text-base md:text-lg text-charcoal font-body max-w-4xl mx-auto leading-relaxed">
              Experience North Bengaluru's most promising investment opportunity
              - a 30-acre premium plotted development strategically located near
              major employment hubs.
            </p>
          </motion.div>

          <motion.div variants={itemVariants}>
            {properties.map((property) => (
              <div
                key={property.id}
                className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 items-stretch bg-white rounded-2xl shadow-xl p-4 lg:p-6 max-h-[90vh] lg:max-h-[70vh] overflow-hidden"
              >
                {/* Images Section */}
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  className="flex flex-col space-y-4 h-full"
                >
                  {/* Main Image */}
                  <div className="relative overflow-hidden rounded-2xl shadow-luxury flex-1">
                    <img
                      src={property.images[0]}
                      alt={property.title}
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-700 min-h-[250px]"
                    />
                    <div className="absolute top-3 left-3 bg-secondary text-primary px-3 py-1 rounded-full text-sm font-semibold">
                      {property.status === "pre-launch"
                        ? "Pre-Launch"
                        : property.status}
                    </div>
                  </div>

                  {/* Gallery Images */}
                  <div className="grid grid-cols-3 gap-3">
                    {property.images.slice(1, 4).map((image, imgIndex) => (
                      <div
                        key={imgIndex}
                        className="relative overflow-hidden rounded-lg shadow-md"
                      >
                        <img
                          src={image}
                          alt={`${property.title} ${imgIndex + 2}`}
                          className="w-full h-16 object-cover hover:scale-105 transition-transform duration-500"
                        />
                      </div>
                    ))}
                  </div>
                </motion.div>

                {/* Content Section */}
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="flex flex-col space-y-4 h-full justify-between"
                >
                  <div>
                    <h3 className="text-2xl md:text-3xl font-playfair font-bold text-primary mb-3">
                      {property.title}
                    </h3>
                    <p className="text-charcoal/80 font-montserrat text-base leading-relaxed">
                      {property.description}
                    </p>
                  </div>

                  {/* Key Stats */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="text-center p-3 bg-primary/5 rounded-lg">
                      <div className="text-xl font-playfair font-bold text-primary mb-1">
                        ₹{(property.price / 10000000).toFixed(1)} Cr
                      </div>
                      <div className="text-xs text-charcoal/70">
                        Starting Price
                      </div>
                    </div>
                    <div className="text-center p-3 bg-primary/5 rounded-lg">
                      <div className="text-xl font-playfair font-bold text-primary mb-1">
                        {property.totalAcres}
                      </div>
                      <div className="text-xs text-charcoal/70">
                        Total Acres
                      </div>
                    </div>
                    <div className="text-center p-3 bg-primary/5 rounded-lg">
                      <div className="text-xl font-playfair font-bold text-primary mb-1">
                        {property.phases?.length || 0}
                      </div>
                      <div className="text-xs text-charcoal/70">Phases</div>
                    </div>
                    <div className="text-center p-3 bg-primary/5 rounded-lg">
                      <div className="text-xl font-playfair font-bold text-primary mb-1">
                        20 min
                      </div>
                      <div className="text-xs text-charcoal/70">To Airport</div>
                    </div>
                  </div>

                  {/* Key Features */}
                  <div className="flex-1">
                    <h4 className="text-lg font-playfair font-semibold text-primary mb-3">
                      Key Features
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {property.features
                        .slice(0, 6)
                        .map((feature, featureIndex) => (
                          <div
                            key={featureIndex}
                            className="flex items-center space-x-2"
                          >
                            <div className="w-1.5 h-1.5 bg-secondary rounded-full flex-shrink-0"></div>
                            <span className="text-charcoal/80 font-montserrat text-xs">
                              {feature}
                            </span>
                          </div>
                        ))}
                    </div>
                  </div>

                  {/* CTA Buttons */}
                  <div className="flex space-x-3 mt-auto">
                    <motion.button
                      className="flex-1 bg-primary hover:bg-primary/90 text-accent px-4 py-2.5 rounded-lg font-montserrat font-semibold transition-all duration-300 text-sm"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Schedule Site Visit
                    </motion.button>
                    <motion.button
                      className="flex-1 border border-primary text-primary hover:bg-primary/5 px-4 py-2.5 rounded-lg font-montserrat font-semibold transition-all duration-300 text-sm"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Download Brochure
                    </motion.button>
                  </div>
                </motion.div>
              </div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Properties;
