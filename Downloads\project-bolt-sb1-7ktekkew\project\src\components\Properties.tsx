import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { properties } from "../data/properties";

const Properties = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94] as const,
      },
    },
  };

  return (
    <section id="properties" className="py-20 bg-accent">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-12"
        >
          <motion.div variants={itemVariants} className="text-center space-y-6">
            <h2 className="text-section-mobile md:text-section font-heading font-bold text-primary">
              Devanahalli Premium Plotted Development
            </h2>
            <div className="w-20 h-1 bg-secondary mx-auto" />
            <p className="text-body-lg md:text-body-lg text-charcoal font-body max-w-3xl mx-auto">
              Experience North Bengaluru's most promising investment opportunity
              - a 30-acre premium plotted development in Devanahalli,
              strategically located near major employment hubs and offering
              exceptional growth potential.
            </p>
          </motion.div>

          <motion.div variants={itemVariants}>
            {properties.map((property) => (
              <div
                key={property.id}
                className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
              >
                {/* Images Section */}
                <motion.div
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  className="space-y-6"
                >
                  {/* Main Image */}
                  <div className="relative overflow-hidden rounded-2xl shadow-luxury">
                    <img
                      src={property.images[0]}
                      alt={property.title}
                      className="w-full h-80 object-cover hover:scale-105 transition-transform duration-700"
                    />
                    <div className="absolute top-4 left-4 bg-secondary text-primary px-3 py-1 rounded-full text-sm font-semibold">
                      {property.status === "pre-launch"
                        ? "Pre-Launch"
                        : property.status}
                    </div>
                  </div>

                  {/* Gallery Images */}
                  <div className="grid grid-cols-3 gap-4">
                    {property.images.slice(1, 4).map((image, imgIndex) => (
                      <div
                        key={imgIndex}
                        className="relative overflow-hidden rounded-lg shadow-md"
                      >
                        <img
                          src={image}
                          alt={`${property.title} ${imgIndex + 2}`}
                          className="w-full h-24 object-cover hover:scale-105 transition-transform duration-500"
                        />
                      </div>
                    ))}
                  </div>
                </motion.div>

                {/* Content Section */}
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                  className="space-y-6"
                >
                  <div>
                    <h3 className="text-3xl font-playfair font-bold text-primary mb-4">
                      {property.title}
                    </h3>
                    <p className="text-charcoal/80 font-montserrat text-lg leading-relaxed">
                      {property.description}
                    </p>
                  </div>

                  {/* Key Stats */}
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center p-4 bg-primary/5 rounded-lg">
                      <div className="text-2xl font-playfair font-bold text-primary mb-1">
                        ₹{(property.price / 10000000).toFixed(1)} Cr
                      </div>
                      <div className="text-sm text-charcoal/70">
                        Starting Price
                      </div>
                    </div>
                    <div className="text-center p-4 bg-primary/5 rounded-lg">
                      <div className="text-2xl font-playfair font-bold text-primary mb-1">
                        {property.totalAcres}
                      </div>
                      <div className="text-sm text-charcoal/70">
                        Total Acres
                      </div>
                    </div>
                    <div className="text-center p-4 bg-primary/5 rounded-lg">
                      <div className="text-2xl font-playfair font-bold text-primary mb-1">
                        {property.phases?.length || 0}
                      </div>
                      <div className="text-sm text-charcoal/70">Phases</div>
                    </div>
                    <div className="text-center p-4 bg-primary/5 rounded-lg">
                      <div className="text-2xl font-playfair font-bold text-primary mb-1">
                        20 min
                      </div>
                      <div className="text-sm text-charcoal/70">To Airport</div>
                    </div>
                  </div>

                  {/* Key Features */}
                  <div>
                    <h4 className="text-xl font-playfair font-semibold text-primary mb-4">
                      Key Features
                    </h4>
                    <div className="grid grid-cols-2 gap-3">
                      {property.features
                        .slice(0, 6)
                        .map((feature, featureIndex) => (
                          <div
                            key={featureIndex}
                            className="flex items-center space-x-2"
                          >
                            <div className="w-2 h-2 bg-secondary rounded-full"></div>
                            <span className="text-charcoal/80 font-montserrat text-sm">
                              {feature}
                            </span>
                          </div>
                        ))}
                    </div>
                  </div>

                  {/* CTA Buttons */}
                  <div className="flex space-x-4">
                    <motion.button
                      className="flex-1 bg-primary hover:bg-primary/90 text-accent px-6 py-3 rounded-lg font-montserrat font-semibold transition-all duration-300"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Schedule Site Visit
                    </motion.button>
                    <motion.button
                      className="flex-1 border border-primary text-primary hover:bg-primary/5 px-6 py-3 rounded-lg font-montserrat font-semibold transition-all duration-300"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      Download Brochure
                    </motion.button>
                  </div>
                </motion.div>
              </div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Properties;
