import React, { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  Send,
  CheckCircle,
  MessageCircle,
  Calendar,
  User,
  MessageSquare,
} from "lucide-react";

const Contact = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [formStep, setFormStep] = useState(1);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    inquiryType: "",
    message: "",
    preferredContact: "",
    visitDate: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
    }, 2000);
  };

  const nextStep = () => {
    if (formStep < 3) {
      setFormStep(formStep + 1);
    }
  };

  const prevStep = () => {
    if (formStep > 1) {
      setFormStep(formStep - 1);
    }
  };

  const contactInfo = [
    {
      icon: Phone,
      title: "Phone",
      details: ["+****************", "+****************"],
      color: "bg-blue-500/20 text-blue-400",
    },
    {
      icon: Mail,
      title: "Email",
      details: ["<EMAIL>", "<EMAIL>"],
      color: "bg-green-500/20 text-green-400",
    },
    {
      icon: MapPin,
      title: "Location",
      details: ["1 Ocean Heights Boulevard", "Oceanview Heights, CA 90210"],
      color: "bg-red-500/20 text-red-400",
    },
    {
      icon: Clock,
      title: "Hours",
      details: ["Mon-Fri: 9AM-7PM", "Sat-Sun: 10AM-6PM"],
      color: "bg-purple-500/20 text-purple-400",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  if (isSubmitted) {
    return (
      <section id="contact" className="py-20 bg-accent">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8 }}
            className="text-center space-y-8 bg-white rounded-2xl p-12 shadow-2xl"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 300 }}
              className="w-24 h-24 bg-green-500/20 rounded-full flex items-center justify-center mx-auto"
            >
              <CheckCircle className="h-12 w-12 text-green-500" />
            </motion.div>

            <div className="space-y-4">
              <h2 className="text-heading-lg font-heading font-bold text-primary">
                Thank You!
              </h2>
              <p className="text-body text-charcoal font-montserrat">
                Your inquiry has been received. Our luxury real estate
                specialist will contact you within 24 hours to discuss your
                exclusive viewing appointment.
              </p>
            </div>

            <motion.button
              onClick={() => {
                setIsSubmitted(false);
                setFormStep(1);
                setFormData({
                  firstName: "",
                  lastName: "",
                  email: "",
                  phone: "",
                  inquiryType: "",
                  message: "",
                  preferredContact: "",
                  visitDate: "",
                });
              }}
              className="bg-primary hover:bg-primary/90 text-accent px-8 py-3 rounded-lg font-montserrat font-semibold transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Send Another Message
            </motion.button>
          </motion.div>
        </div>
      </section>
    );
  }

  return (
    <section id="contact" className="py-20 bg-accent">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-16"
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center space-y-6">
            <h2 className="text-section-mobile md:text-section font-heading font-bold text-primary">
              Contact Us
            </h2>
            <div className="w-20 h-1 bg-secondary mx-auto" />
            <p className="text-body-lg md:text-body-lg text-charcoal font-body max-w-2xl mx-auto">
              Ready to experience luxury living? Get in touch with our dedicated
              team to schedule your private viewing or learn more about
              Signature Villa.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <motion.div variants={itemVariants} className="space-y-8">
              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <motion.div
                    key={index}
                    className="flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
                    whileHover={{ y: -2 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div
                      className={`w-12 h-12 rounded-full ${info.color} flex items-center justify-center flex-shrink-0`}
                    >
                      <info.icon className="h-6 w-6" />
                    </div>
                    <div className="space-y-1">
                      <h4 className="text-lg font-playfair font-semibold text-primary">
                        {info.title}
                      </h4>
                      {info.details.map((detail, detailIndex) => (
                        <p
                          key={detailIndex}
                          className="text-charcoal font-montserrat text-sm"
                        >
                          {detail}
                        </p>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Quick Actions */}
              <div className="space-y-4">
                <h3 className="text-xl font-playfair font-semibold text-primary">
                  Quick Actions
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <motion.button
                    className="flex items-center space-x-3 p-4 bg-primary text-accent rounded-lg hover:bg-primary/90 transition-all duration-300"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <MessageCircle className="h-5 w-5" />
                    <span className="font-montserrat font-medium">
                      WhatsApp
                    </span>
                  </motion.button>

                  <motion.button
                    className="flex items-center space-x-3 p-4 bg-secondary text-primary rounded-lg hover:bg-secondary/90 transition-all duration-300"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Calendar className="h-5 w-5" />
                    <span className="font-montserrat font-medium">
                      Book Tour
                    </span>
                  </motion.button>
                </div>
              </div>
            </motion.div>

            {/* Multi-Step Form */}
            <motion.div
              variants={itemVariants}
              className="bg-white rounded-2xl p-8 shadow-xl"
            >
              <div className="space-y-6">
                {/* Progress Bar */}
                <div className="flex items-center justify-between mb-8">
                  {[1, 2, 3].map((step) => (
                    <div key={step} className="flex items-center">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-montserrat font-semibold ${
                          step <= formStep
                            ? "bg-primary text-accent"
                            : "bg-gray-200 text-gray-400"
                        }`}
                      >
                        {step}
                      </div>
                      {step < 3 && (
                        <div
                          className={`w-16 h-1 mx-2 ${
                            step < formStep ? "bg-primary" : "bg-gray-200"
                          }`}
                        />
                      )}
                    </div>
                  ))}
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Step 1: Personal Information */}
                  {formStep === 1 && (
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5 }}
                      className="space-y-4"
                    >
                      <h3 className="text-xl font-playfair font-semibold text-primary flex items-center">
                        <User className="h-5 w-5 mr-2" />
                        Personal Information
                      </h3>

                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                            First Name *
                          </label>
                          <input
                            type="text"
                            name="firstName"
                            value={formData.firstName}
                            onChange={handleInputChange}
                            required
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 font-montserrat"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                            Last Name *
                          </label>
                          <input
                            type="text"
                            name="lastName"
                            value={formData.lastName}
                            onChange={handleInputChange}
                            required
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 font-montserrat"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                          Email Address *
                        </label>
                        <input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 font-montserrat"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                          Phone Number *
                        </label>
                        <input
                          type="tel"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 font-montserrat"
                        />
                      </div>
                    </motion.div>
                  )}

                  {/* Step 2: Inquiry Details */}
                  {formStep === 2 && (
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5 }}
                      className="space-y-4"
                    >
                      <h3 className="text-xl font-playfair font-semibold text-primary flex items-center">
                        <MessageSquare className="h-5 w-5 mr-2" />
                        Inquiry Details
                      </h3>

                      <div>
                        <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                          Inquiry Type *
                        </label>
                        <select
                          name="inquiryType"
                          value={formData.inquiryType}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 font-montserrat"
                        >
                          <option value="">Select inquiry type</option>
                          <option value="purchase">Purchase Information</option>
                          <option value="viewing">Schedule Viewing</option>
                          <option value="investment">
                            Investment Opportunity
                          </option>
                          <option value="other">Other</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                          Preferred Contact Method
                        </label>
                        <select
                          name="preferredContact"
                          value={formData.preferredContact}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 font-montserrat"
                        >
                          <option value="">Select preference</option>
                          <option value="email">Email</option>
                          <option value="phone">Phone Call</option>
                          <option value="whatsapp">WhatsApp</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                          Preferred Visit Date
                        </label>
                        <input
                          type="date"
                          name="visitDate"
                          value={formData.visitDate}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 font-montserrat"
                        />
                      </div>
                    </motion.div>
                  )}

                  {/* Step 3: Message */}
                  {formStep === 3 && (
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5 }}
                      className="space-y-4"
                    >
                      <h3 className="text-xl font-playfair font-semibold text-primary flex items-center">
                        <Send className="h-5 w-5 mr-2" />
                        Your Message
                      </h3>

                      <div>
                        <label className="block text-sm font-montserrat font-medium text-charcoal mb-2">
                          Message
                        </label>
                        <textarea
                          name="message"
                          value={formData.message}
                          onChange={handleInputChange}
                          rows={6}
                          placeholder="Tell us about your requirements, timeline, or any specific questions..."
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 font-montserrat resize-none"
                        />
                      </div>
                    </motion.div>
                  )}

                  {/* Navigation Buttons */}
                  <div className="flex justify-between pt-6">
                    {formStep > 1 && (
                      <motion.button
                        type="button"
                        onClick={prevStep}
                        className="px-6 py-3 border border-primary text-primary rounded-lg font-montserrat font-semibold hover:bg-primary hover:text-accent transition-all duration-300"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        Previous
                      </motion.button>
                    )}

                    {formStep < 3 ? (
                      <motion.button
                        type="button"
                        onClick={nextStep}
                        className="ml-auto px-6 py-3 bg-primary text-accent rounded-lg font-montserrat font-semibold hover:bg-primary/90 transition-all duration-300"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        Next
                      </motion.button>
                    ) : (
                      <motion.button
                        type="submit"
                        disabled={isSubmitting}
                        className="ml-auto px-6 py-3 bg-secondary text-primary rounded-lg font-montserrat font-semibold hover:bg-secondary/90 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                        whileHover={{ scale: isSubmitting ? 1 : 1.05 }}
                        whileTap={{ scale: isSubmitting ? 1 : 0.95 }}
                      >
                        {isSubmitting ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary" />
                            <span>Sending...</span>
                          </>
                        ) : (
                          <>
                            <Send className="h-4 w-4" />
                            <span>Send Message</span>
                          </>
                        )}
                      </motion.button>
                    )}
                  </div>
                </form>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Contact;
