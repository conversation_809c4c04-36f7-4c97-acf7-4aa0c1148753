import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Maximize2, Bed, Bath, Car, Ruler } from "lucide-react";

const Property = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [activePanel, setActivePanel] = useState(0);

  const panels = [
    {
      id: "exterior",
      title: "Exterior Design",
      image:
        "https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=800",
      features: [
        "Ocean Front Location",
        "Private Beach Access",
        "Infinity Pool",
        "Landscaped Gardens",
      ],
      description:
        "Stunning architectural design with seamless indoor-outdoor living spaces.",
    },
    {
      id: "interior",
      title: "Interior Spaces",
      image:
        "https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=800",
      features: [
        "Open Floor Plans",
        "Premium Finishes",
        "Smart Home Technology",
        "Custom Built-ins",
      ],
      description:
        "Meticulously designed interiors featuring the finest materials and craftsmanship.",
    },
    {
      id: "amenities",
      title: "Luxury Amenities",
      image:
        "https://images.pexels.com/photos/1571468/pexels-photo-1571468.jpeg?auto=compress&cs=tinysrgb&w=800",
      features: [
        "Home Theater",
        "Wine Cellar",
        "Spa & Wellness",
        "Private Gym",
      ],
      description:
        "World-class amenities designed for the ultimate luxury lifestyle experience.",
    },
  ];

  const specifications = [
    { icon: Bed, label: "Bedrooms", value: "5" },
    { icon: Bath, label: "Bathrooms", value: "6" },
    { icon: Car, label: "Garage", value: "3" },
    { icon: Ruler, label: "Living Area", value: "6,500 sq ft" },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="property" className="py-20 bg-primary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="space-y-16"
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center space-y-6">
            <h2 className="text-section-mobile md:text-section font-playfair font-bold text-accent">
              Property Overview
            </h2>
            <div className="w-20 h-1 bg-secondary mx-auto" />
            <p className="text-body-mobile md:text-body text-accent/90 font-montserrat max-w-2xl mx-auto">
              Discover the extraordinary features and amenities that make
              Signature Villa a true masterpiece of luxury living.
            </p>
          </motion.div>

          {/* Property Panels */}
          <motion.div variants={itemVariants} className="space-y-8">
            {/* Panel Navigation */}
            <div className="flex justify-center">
              <div className="flex space-x-1 bg-white/10 backdrop-blur-sm rounded-full p-2">
                {panels.map((panel, index) => (
                  <motion.button
                    key={panel.id}
                    onClick={() => setActivePanel(index)}
                    className={`px-6 py-3 rounded-full font-montserrat font-medium transition-all duration-300 ${
                      activePanel === index
                        ? "bg-secondary text-primary shadow-lg"
                        : "text-accent hover:text-secondary"
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {panel.title}
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Active Panel Content */}
            <AnimatePresence mode="wait">
              <motion.div
                key={activePanel}
                initial={{ opacity: 0, x: 100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -100 }}
                transition={{ duration: 0.5 }}
                className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
              >
                {/* Image */}
                <motion.div
                  className="relative overflow-hidden rounded-2xl shadow-2xl"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.4 }}
                >
                  <img
                    src={panels[activePanel].image}
                    alt={panels[activePanel].title}
                    className="w-full h-96 object-cover"
                    loading="lazy"
                  />

                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-primary/50 to-transparent" />

                  {/* Expand Button */}
                  <motion.button
                    className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm rounded-full p-3 text-accent hover:bg-white/30 transition-all duration-300"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Maximize2 className="h-5 w-5" />
                  </motion.button>
                </motion.div>

                {/* Content */}
                <div className="space-y-6">
                  <div>
                    <h3 className="text-2xl font-playfair font-bold text-accent mb-4">
                      {panels[activePanel].title}
                    </h3>
                    <p className="text-body-mobile md:text-body text-accent/90 font-montserrat leading-relaxed">
                      {panels[activePanel].description}
                    </p>
                  </div>

                  {/* Features */}
                  <div className="grid grid-cols-2 gap-4">
                    {panels[activePanel].features.map((feature, index) => (
                      <motion.div
                        key={index}
                        className="flex items-center space-x-2"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                      >
                        <div className="w-2 h-2 bg-secondary rounded-full" />
                        <span className="text-accent font-montserrat text-sm">
                          {feature}
                        </span>
                      </motion.div>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <motion.button
                    className="bg-secondary hover:bg-secondary/90 text-primary px-8 py-4 rounded-lg font-montserrat font-semibold transition-all duration-300 w-full sm:w-auto"
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    View 3D Virtual Tour
                  </motion.button>
                </div>
              </motion.div>
            </AnimatePresence>
          </motion.div>

          {/* Specifications */}
          <motion.div variants={itemVariants} className="space-y-8">
            <h3 className="text-xl font-playfair font-bold text-accent text-center">
              Property Specifications
            </h3>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {specifications.map((spec, index) => (
                <motion.div
                  key={index}
                  className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center hover:bg-white/20 transition-all duration-300"
                  whileHover={{ y: -5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <spec.icon className="h-8 w-8 text-secondary mx-auto mb-3" />
                  <div className="text-2xl font-playfair font-bold text-accent mb-1">
                    {spec.value}
                  </div>
                  <div className="text-sm font-montserrat text-accent/70">
                    {spec.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Property;
