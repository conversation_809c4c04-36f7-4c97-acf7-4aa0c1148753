export interface Property {
  id: string;
  title: string;
  price: number;
  location: string;
  type: 'house' | 'apartment' | 'villa' | 'penthouse' | 'commercial';
  bedrooms: number;
  bathrooms: number;
  area: number;
  images: string[];
  videoUrl?: string;
  description: string;
  features: string[];
  coordinates: {
    lat: number;
    lng: number;
  };
  status: 'for-sale' | 'for-rent' | 'sold' | 'rented';
  featured: boolean;
  yearBuilt: number;
  parking: number;
}

export interface Agent {
  id: string;
  name: string;
  title: string;
  image: string;
  phone: string;
  email: string;
  specialties: string[];
  experience: number;
  languages: string[];
  bio: string;
}

export interface Testimonial {
  id: string;
  name: string;
  title: string;
  image: string;
  rating: number;
  text: string;
  videoUrl?: string;
  location: string;
  propertyType: string;
}

export interface SearchFilters {
  location: string;
  priceMin: number;
  priceMax: number;
  type: string;
  bedrooms: number;
  bathrooms: number;
  areaMin: number;
  areaMax: number;
  status: string;
}