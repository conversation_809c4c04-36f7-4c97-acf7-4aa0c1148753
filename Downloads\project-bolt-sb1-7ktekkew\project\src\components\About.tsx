import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";

const About = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="about" className="py-20 bg-accent">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-stretch"
        >
          {/* Content Column */}
          <motion.div
            variants={itemVariants}
            className="flex flex-col justify-center space-y-8 lg:pr-8"
          >
            <div className="space-y-6">
              <motion.h2
                className="text-section-mobile md:text-section font-heading font-bold text-primary"
                variants={itemVariants}
              >
                Defining Luxury Living
              </motion.h2>

              <motion.div
                className="w-20 h-1 bg-secondary"
                variants={itemVariants}
              />

              <motion.p
                className="text-body-lg md:text-body-lg text-charcoal font-body"
                variants={itemVariants}
              >
                Shreyas Properties represents the pinnacle of luxury real estate
                in India, where architectural brilliance meets sophisticated
                urban living. Every detail has been meticulously crafted to
                create an unparalleled residential experience that transcends
                traditional luxury standards.
              </motion.p>

              <motion.p
                className="text-body-lg md:text-body-lg text-charcoal font-body"
                variants={itemVariants}
              >
                From the moment you step inside, you're enveloped in an
                atmosphere of refined elegance. Floor-to-ceiling windows frame
                stunning city vistas, while premium materials and bespoke
                finishes reflect our commitment to exceptional quality and
                timeless design across India's most prestigious locations.
              </motion.p>
            </div>
          </motion.div>

          {/* Image Column */}
          <motion.div
            variants={itemVariants}
            className="flex items-center justify-center lg:pl-8"
          >
            <motion.div
              className="relative overflow-hidden rounded-2xl shadow-2xl w-full h-[300px] lg:h-[400px]"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.4 }}
            >
              <img
                src="https://images.pexels.com/photos/1571463/pexels-photo-1571463.jpeg?auto=compress&cs=tinysrgb&w=800"
                alt="Luxury property interior showcasing modern design"
                className="w-full h-[300px] lg:h-[400px] object-cover"
                loading="lazy"
              />

              {/* Subtle gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent" />
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
